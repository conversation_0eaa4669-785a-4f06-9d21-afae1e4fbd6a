# Vue i18n with New Lines - Complete Guide

This guide demonstrates various methods to handle new lines in Vue i18n translations.

## Methods Overview

### Method 1: Using `\n` with CSS `white-space`

**Best for:** Simple text with line breaks, no HTML formatting needed.

**i18n JSON:**
```json
{
  "WELCOME_MESSAGE": "Welcome to our platform!\nWe're glad to have you here."
}
```

**Template:**
```vue
<template>
  <p class="whitespace-pre-line">{{ t('WELCOME_MESSAGE') }}</p>
</template>
```

**CSS Classes:**
- `whitespace-pre-line` - Preserves line breaks and wraps text
- `whitespace-pre` - Preserves all whitespace and line breaks
- `whitespace-pre-wrap` - Preserves line breaks and wraps text

### Method 2: Using HTML with `v-html`

**Best for:** Rich text with HTML formatting and line breaks.

**i18n JSON:**
```json
{
  "HTML_MESSAGE": "Welcome!<br><strong>Bold text</strong><br><em>Italic text</em>"
}
```

**Template:**
```vue
<template>
  <p v-html="t('HTML_MESSAGE')"></p>
</template>
```

**⚠️ Security Note:** Only use `v-html` with trusted content to prevent XSS attacks.

### Method 3: Using Computed Property to Convert `\n` to `<br>`

**Best for:** Dynamic conversion of plain text to HTML.

**i18n JSON:**
```json
{
  "TERMS_TEXT": "By continuing, you agree to our\nTerms of Service and Privacy Policy."
}
```

**Template:**
```vue
<template>
  <p v-html="convertNewlinesToBr(t('TERMS_TEXT'))"></p>
</template>

<script setup>
const convertNewlinesToBr = (text) => {
  return text.replace(/\n/g, '<br>')
}
</script>
```

### Method 4: Using Template with Multiple Elements

**Best for:** When you need different styling for each line.

**Template:**
```vue
<template>
  <div>
    <p>{{ t('WELCOME_MESSAGE').split('\n')[0] }}</p>
    <p class="text-blue-500">{{ t('WELCOME_MESSAGE').split('\n')[1] }}</p>
  </div>
</template>
```

### Method 5: Using Vue i18n Interpolation with Components

**Best for:** Complex layouts with different components for each part.

**i18n JSON:**
```json
{
  "INTERPOLATION_EXAMPLE": "{line1}{line2}"
}
```

**Template:**
```vue
<template>
  <i18n-t keypath="INTERPOLATION_EXAMPLE" tag="div">
    <template #line1>
      <p class="font-bold text-blue-600">First line with styling</p>
    </template>
    <template #line2>
      <p class="italic text-green-600">Second line with different styling</p>
    </template>
  </i18n-t>
</template>
```

### Method 6: Using Custom Directive

**Best for:** Reusable solution across multiple components.

**Template:**
```vue
<template>
  <p v-newline="t('MULTILINE_DESCRIPTION')"></p>
</template>

<script setup>
const vNewline = {
  mounted(el, binding) {
    el.innerHTML = binding.value.replace(/\n/g, '<br>')
  },
  updated(el, binding) {
    el.innerHTML = binding.value.replace(/\n/g, '<br>')
  }
}
</script>
```

## Advanced Techniques

### Global Custom Directive

Create a global directive in `main.ts`:

```typescript
// main.ts
app.directive('newline', {
  mounted(el: HTMLElement, binding: any) {
    el.innerHTML = binding.value.replace(/\n/g, '<br>')
  },
  updated(el: HTMLElement, binding: any) {
    el.innerHTML = binding.value.replace(/\n/g, '<br>')
  }
})
```

### Composable for Text Formatting

```typescript
// composables/useTextFormatting.ts
export function useTextFormatting() {
  const formatNewlines = (text: string): string => {
    return text.replace(/\n/g, '<br>')
  }
  
  const formatWithParagraphs = (text: string): string => {
    return text.split('\n').map(line => `<p>${line}</p>`).join('')
  }
  
  return {
    formatNewlines,
    formatWithParagraphs
  }
}
```

### Plugin for Global Text Formatting

```typescript
// plugins/textFormatting.ts
export default {
  install(app: any) {
    app.config.globalProperties.$formatText = (text: string) => {
      return text.replace(/\n/g, '<br>')
    }
  }
}
```

## Best Practices

1. **Use Method 1** for simple text with line breaks
2. **Use Method 2** for rich text with HTML formatting
3. **Use Method 5** for complex layouts with different styling per line
4. **Always sanitize** user-generated content before using `v-html`
5. **Consider accessibility** when using custom formatting
6. **Test on different screen sizes** to ensure proper line breaking

## Example Usage in Real Components

Visit `/i18n-examples` to see all methods in action with live examples.
