<template>
  <div class="flex min-h-[calc(100vh-5rem)] flex-col w-full items-center justify-between">
    <div id="messages" class="w-full">
      <Message
        v-for="message in messages"
        :key="message.content"
        :role="message.role"
        :content="message.content"
        :is-last-message="true"
        :timestamp="new Date().getTime()"
      />
    </div>
    <form @submit="onSubmit" class="flex flex-row w-full px-2">
      <Input type="text" :placeholder="t('ENTER_YOUR_MESSAGE')" v-model="newMessage"/>
      <Button type="submit">
        <SendHorizontal class="h-4 w-4"/>
      </Button>
    </form>
  </div>
</template>

<script setup lang="ts">
import {type Ref, ref} from 'vue'
import {Message} from '@/components/ui/message'
import {Input} from "@/components/ui/input";

import {useI18n} from "vue-i18n";
import {Button} from "@/components/ui/button";

import {SendHorizontal} from "lucide-vue-next";

const {t} = useI18n()

const newMessage = ref('')

const messages: Ref<{ role: 'user' | 'assistant'; content: string; timestamp: number }[]> = ref([
  {
    role: 'user',
    content: 'Hello',
    timestamp: new Date().getTime(),
  },
  {
    role: 'assistant',
    content: 'Hello',
    timestamp: new Date().getTime(),
  },
])

const onSubmit = (e: Event) => {
  e.preventDefault()
  if (!newMessage.value) return

  messages.value.push({
    role: messages.value.length % 2 === 0 ? 'user' : 'assistant',
    content: newMessage.value,
    timestamp: new Date().getTime(),
  })
}
</script>

<style scoped></style>
