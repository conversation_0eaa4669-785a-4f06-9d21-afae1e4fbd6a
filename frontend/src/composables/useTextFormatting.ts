/**
 * Composable for text formatting utilities
 * Provides various methods to format text with new lines and HTML
 */
export function useTextFormatting() {
  /**
   * Convert \n characters to <br> HTML tags
   * @param text - Text with \n characters
   * @returns HTML string with <br> tags
   */
  const formatNewlines = (text: string): string => {
    return text.replace(/\n/g, '<br>')
  }

  /**
   * Convert \n characters to separate <p> elements
   * @param text - Text with \n characters
   * @returns HTML string with <p> tags
   */
  const formatWithParagraphs = (text: string): string => {
    return text
      .split('\n')
      .filter(line => line.trim() !== '') // Remove empty lines
      .map(line => `<p>${line.trim()}</p>`)
      .join('')
  }

  /**
   * Convert \n characters to <div> elements
   * @param text - Text with \n characters
   * @returns HTML string with <div> tags
   */
  const formatWithDivs = (text: string): string => {
    return text
      .split('\n')
      .filter(line => line.trim() !== '')
      .map(line => `<div>${line.trim()}</div>`)
      .join('')
  }

  /**
   * Split text by \n and return as array
   * Useful for v-for loops in templates
   * @param text - Text with \n characters
   * @returns Array of text lines
   */
  const splitLines = (text: string): string[] => {
    return text.split('\n').filter(line => line.trim() !== '')
  }

  /**
   * Format text with custom separator
   * @param text - Text with \n characters
   * @param separator - Custom HTML separator
   * @returns HTML string with custom separator
   */
  const formatWithCustomSeparator = (text: string, separator: string = '<br>'): string => {
    return text.replace(/\n/g, separator)
  }

  /**
   * Escape HTML and then convert \n to <br>
   * Safe for user-generated content
   * @param text - Text with \n characters
   * @returns Escaped HTML string with <br> tags
   */
  const formatNewlinesSafe = (text: string): string => {
    const escaped = text
      .replace(/&/g, '&amp;')
      .replace(/</g, '&lt;')
      .replace(/>/g, '&gt;')
      .replace(/"/g, '&quot;')
      .replace(/'/g, '&#039;')
    
    return escaped.replace(/\n/g, '<br>')
  }

  return {
    formatNewlines,
    formatWithParagraphs,
    formatWithDivs,
    splitLines,
    formatWithCustomSeparator,
    formatNewlinesSafe
  }
}
