<template>
  <div class="p-8 space-y-8">
    <h1 class="text-3xl font-bold">Vue i18n New Line Examples</h1>
    
    <!-- Method 1: Using \n with CSS white-space -->
    <div class="space-y-4">
      <h2 class="text-xl font-semibold">Method 1: Using \n with CSS white-space</h2>
      <div class="p-4 border rounded-lg bg-gray-50">
        <p class="whitespace-pre-line">{{ t('WELCOME_MESSAGE') }}</p>
      </div>
      <div class="p-4 border rounded-lg bg-gray-50">
        <p class="whitespace-pre-line">{{ t('MULTILINE_DESCRIPTION') }}</p>
      </div>
    </div>

    <!-- Method 2: Using HTML with v-html -->
    <div class="space-y-4">
      <h2 class="text-xl font-semibold">Method 2: Using HTML with v-html</h2>
      <div class="p-4 border rounded-lg bg-gray-50">
        <p v-html="t('HTML_NEWLINE_MESSAGE')"></p>
      </div>
      <div class="p-4 border rounded-lg bg-gray-50">
        <p v-html="t('FORMATTED_TEXT')"></p>
      </div>
    </div>

    <!-- Method 3: Using computed property to replace \n with <br> -->
    <div class="space-y-4">
      <h2 class="text-xl font-semibold">Method 3: Using computed property to replace \n with &lt;br&gt;</h2>
      <div class="p-4 border rounded-lg bg-gray-50">
        <p v-html="convertNewlinesToBr(t('TERMS_PREVIEW'))"></p>
      </div>
    </div>

    <!-- Method 4: Using template with multiple elements -->
    <div class="space-y-4">
      <h2 class="text-xl font-semibold">Method 4: Using template with multiple elements</h2>
      <div class="p-4 border rounded-lg bg-gray-50">
        <div>
          <p>{{ t('WELCOME_MESSAGE').split('\n')[0] }}</p>
          <p>{{ t('WELCOME_MESSAGE').split('\n')[1] }}</p>
        </div>
      </div>
    </div>

    <!-- Method 5: Using Vue i18n interpolation with components -->
    <div class="space-y-4">
      <h2 class="text-xl font-semibold">Method 5: Using Vue i18n interpolation with components</h2>
      <div class="p-4 border rounded-lg bg-gray-50">
        <i18n-t keypath="INTERPOLATION_EXAMPLE" tag="div">
          <template #line1>
            <p class="font-bold text-blue-600">First line with styling</p>
          </template>
          <template #line2>
            <p class="italic text-green-600">Second line with different styling</p>
          </template>
        </i18n-t>
      </div>
    </div>

    <!-- Method 6: Using custom directive -->
    <div class="space-y-4">
      <h2 class="text-xl font-semibold">Method 6: Using custom directive</h2>
      <div class="p-4 border rounded-lg bg-gray-50">
        <p v-newline="t('MULTILINE_DESCRIPTION')"></p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useI18n } from 'vue-i18n'

const { t } = useI18n()

// Method 3: Convert \n to <br> tags
const convertNewlinesToBr = (text: string): string => {
  return text.replace(/\n/g, '<br>')
}

// Custom directive for handling newlines (Method 6)
const vNewline = {
  mounted(el: HTMLElement, binding: any) {
    el.innerHTML = binding.value.replace(/\n/g, '<br>')
  },
  updated(el: HTMLElement, binding: any) {
    el.innerHTML = binding.value.replace(/\n/g, '<br>')
  }
}
</script>

<style scoped>
/* Additional styles for better presentation */
.space-y-8 > * + * {
  margin-top: 2rem;
}

.space-y-4 > * + * {
  margin-top: 1rem;
}
</style>
