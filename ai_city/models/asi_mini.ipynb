{"cells": [{"cell_type": "code", "execution_count": 1, "id": "1495b84e", "metadata": {}, "outputs": [], "source": ["import os\n", "from dotenv import load_dotenv\n", "import requests\n", "import json"]}, {"cell_type": "code", "execution_count": 2, "id": "915085df", "metadata": {}, "outputs": [], "source": ["load_dotenv()\n", "\n", "API_KEY = os.getenv('ASI_ONE_KEY')\n", "URL = \"https://api.asi1.ai/v1/chat/completions\""]}, {"cell_type": "code", "execution_count": 3, "id": "22cda430", "metadata": {}, "outputs": [], "source": ["def call_asi1_chatbot(user_message):\n", "    headers = {\n", "        'Content-Type': 'application/json',\n", "        'Authorization': f'<PERSON><PERSON> {API_KEY}'\n", "    }\n", "    payload = json.dumps({\n", "        \"model\": \"asi1-mini\",\n", "        \"messages\": [{\"role\": \"user\", \"content\": user_message}],\n", "        \"temperature\": 0.7,\n", "        \"stream\": <PERSON><PERSON><PERSON>,\n", "        \"max_tokens\": 500\n", "    })\n", "    response = requests.post(URL, headers=headers, data=payload)\n", "    if response.status_code == 200:\n", "        return response.json().get(\"choices\", [{}])[0].get(\"message\", {}).get(\"content\", \"No response\")\n", "    else:\n", "        return f\"Error: {response.status_code}, {response.text}\"\n"]}, {"cell_type": "code", "execution_count": 4, "id": "615de004", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Hello! I'm ASI1-Mini, an advanced AI developed by Fetch.ai Inc with a focus on agentic workflows. I'm here to provide helpful, accurate information about breast cancer.\n", "\n", "Breast cancer is a type of cancer that develops in the cells of the breast. It's one of the most common cancers among women worldwide, though it can also occur in men (though much less frequently). Let me share some key points:\n", "\n", "1. **Types**: There are different types of breast cancer, including ductal carcinoma (which begins in the milk ducts), lobular carcinoma (which starts in the milk-producing glands), and rarer types.\n", "\n", "2. **Symptoms**: Common signs may include a lump in the breast or underarm, changes in breast size or shape, skin dimpling, nipple discharge (not breast milk), or nipple inversion.\n", "\n", "3. **Risk Factors**: While the exact cause isn't always clear, certain factors can increase risk, including age, family history, genetic mutations (like BRCA1 and BRCA2), hormonal factors, and lifestyle choices.\n", "\n", "4. **Screening**: Regular screening (like mammograms) can help detect breast cancer early, when it's most treatable. Recommendations for screening can vary by age and individual risk factors.\n", "\n", "5. **Treatment**: Treatment options have improved significantly and may include surgery, radiation therapy, chemotherapy, hormone therapy, targeted therapy, or a combination of these approaches.\n", "\n", "6. **Prevention**: While not all cases can be prevented, maintaining a healthy lifestyle, including regular exercise, limited alcohol consumption, and maintaining a healthy weight, may help reduce risk.\n", "\n", "7. **Prognosis**: When detected early, breast cancer has a high survival rate. Medical research continues to improve treatment outcomes and quality of life for patients.\n", "\n", "Would you like to know more about specific aspects of breast cancer, such as prevention strategies, treatment advancements, or support resources? I'm here to help you explore any aspect you're interested in.\n"]}], "source": ["print(call_asi1_chatbot(\"Hello, tell me about breast cancer\"))"]}], "metadata": {"kernelspec": {"display_name": "venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.3"}}, "nbformat": 4, "nbformat_minor": 5}